server:
  port: 80
  servlet:
    context-path: /
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url:  ***************************
    username: root
    password: 110122110
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20

mybatis:
  configuration:  # setting??
    auto-mapping-behavior: full
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  type-aliases-package: com.shenxinfu.java.springboot3.pojo # 配置别名
  mapper-locations: classpath:Mapper/*.xml # mapperxml位置