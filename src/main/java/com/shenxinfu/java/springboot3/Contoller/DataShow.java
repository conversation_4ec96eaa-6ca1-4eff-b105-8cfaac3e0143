package com.shenxinfu.java.springboot3.Contoller;
import com.shenxinfu.java.springboot3.Mapper.AccountMapper;
import com.shenxinfu.java.springboot3.pojo.Account;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
public class DataShow {
    @Autowired
    private AccountMapper accountMapper;
    @GetMapping("/show")
    public List<Account> show() {
        return accountMapper.queryAll();
    }
}
