package com.shenxinfu.java.springboot3.Contoller;
import com.shenxinfu.java.springboot3.pojo.Account;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


@RestController
public class DataShow {

    @GetMapping("/show")
    public String show() {
        return "Hello World!";
    }

    @GetMapping("/accounts")
    public List<Account> getAccounts() {
        // 返回模拟数据，不依赖数据库
        List<Account> accounts = new ArrayList<>();
        accounts.add(new Account("张三", 1000));
        accounts.add(new Account("李四", 2000));
        return accounts;
    }
}
